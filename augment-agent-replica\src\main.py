#!/usr/bin/env python3
"""
Main entry point for Augment Agent Replica
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

# Convert relative imports to absolute
try:
    from src.core.agent import AugmentAgentReplica
    from src.utils.config import Config
    from src.utils.logger import setup_logger
    from src.api.server import create_app
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\n🔧 Please install dependencies first:")
    print("   python install_dependencies.py")
    print("   or")
    print("   pip install -r requirements.txt")
    print("\n💡 If you're missing the Mistral AI library:")
    print("   pip install mistralai")
    sys.exit(1) # Exit if imports fail

logger = setup_logger(__name__)

async def interactive_mode(agent: AugmentAgentReplica):
    """Run agent in interactive command-line mode"""
    print("🤖 Augment Agent Replica - Interactive Mode")
    print("Type 'exit' to quit, 'help' for commands")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'q']:
                print("👋 Goodbye!")
                break
            
            if user_input.lower() == 'help':
                print_help()
                continue
            
            if user_input.lower() == 'status':
                status = agent.get_agent_status()
                print(f"📊 Agent Status: {status}")
                continue
            
            if user_input.lower() == 'tools':
                tools = agent.get_available_tools()
                print(f"🔧 Available Tools ({len(tools)}):")
                for tool in tools:
                    print(f"  - {tool['name']} ({tool['category']}): {tool['description'][:100]}...")
                continue
            
            if not user_input:
                continue
            
            print("\n🤖 Agent:", end=" ")
            
            # Stream response
            async for response_chunk in agent.chat(user_input, stream=True):
                if response_chunk['type'] == 'content':
                    print(response_chunk['content'], end='', flush=True)
                elif response_chunk['type'] == 'tool_result':
                    tool_name = response_chunk['tool_name']
                    success = response_chunk['success']
                    status_icon = "✅" if success else "❌"
                    print(f"\n🔧 {status_icon} Tool: {tool_name}")
                    if not success:
                        print(f"   Error: {response_chunk['error']}")
                elif response_chunk['type'] == 'error':
                    print(f"\n❌ Error: {response_chunk['content']}")
            
            print()  # New line after response
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

def print_help():
    """Print help information"""
    help_text = """
🤖 Augment Agent Replica Commands:

Basic Commands:
  help     - Show this help message
  status   - Show agent status and statistics
  tools    - List all available tools
  exit/quit/q - Exit the program

Usage Examples:
  "view src/main.py"
  "help me implement authentication"
  "create a new React component"
  "run tests for the project"
  "explain this code: [paste code]"

The agent has access to 25+ tools including:
  📁 File Management: view, edit, create, delete files
  🧠 Code Intelligence: semantic search, diagnostics
  ⚙️  Process Management: run commands, manage processes
  🌐 Web Integration: search, fetch content
  📋 Task Management: organize complex work
  💾 Memory: remember important information
  📊 Content Analysis: advanced text processing

Just describe what you want to do in natural language!
    """
    print(help_text)

async def single_command_mode(agent: AugmentAgentReplica, command: str):
    """Execute a single command and exit"""
    print(f"🤖 Executing: {command}")
    print("-" * 50)
    
    async for response_chunk in agent.chat(command, stream=True):
        if response_chunk['type'] == 'content':
            print(response_chunk['content'], end='', flush=True)
        elif response_chunk['type'] == 'tool_result':
            tool_name = response_chunk['tool_name']
            success = response_chunk['success']
            status_icon = "✅" if success else "❌"
            print(f"\n🔧 {status_icon} Tool: {tool_name}")
            if not success:
                print(f"   Error: {response_chunk['error']}")
        elif response_chunk['type'] == 'error':
            print(f"\n❌ Error: {response_chunk['content']}")
    
    print()

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Augment Agent Replica - AI Coding Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python src/main.py --interactive
  python src/main.py --command "view src/main.py"
  python src/main.py --server --port 8000
  python src/main.py --workspace /path/to/project
        """
    )
    
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='Run in interactive mode'
    )
    
    parser.add_argument(
        '--command', '-c',
        type=str,
        help='Execute a single command and exit'
    )
    
    parser.add_argument(
        '--server', '-s',
        action='store_true',
        help='Start web server'
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=8000,
        help='Port for web server (default: 8000)'
    )
    
    parser.add_argument(
        '--host',
        type=str,
        default='localhost',
        help='Host for web server (default: localhost)'
    )
    
    parser.add_argument(
        '--workspace', '-w',
        type=str,
        help='Workspace directory path'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Configuration file path'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level'
    )
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        if args.config:
            config = Config.from_file(args.config)
        else:
            config = Config()
        
        # Override config with command line arguments
        if args.workspace:
            config.workspace_root = os.path.abspath(args.workspace)
        if args.port:
            config.port = args.port
        if args.host:
            config.host = args.host
        
        # Set log level
        config.log_level = args.log_level
        
        # Initialize agent
        agent = AugmentAgentReplica(config)
        
        # Run based on mode
        if args.server:
            # Start web server
            print(f"🚀 Starting Augment Agent Replica server on {config.host}:{config.port}")
            app = create_app(agent, config)
            import uvicorn
            uvicorn.run(app, host=config.host, port=config.port, log_level=config.log_level.lower())
            
        elif args.command:
            # Single command mode
            asyncio.run(single_command_mode(agent, args.command))
            
        elif args.interactive:
            # Interactive mode
            asyncio.run(interactive_mode(agent))
            
        else:
            # Default to interactive mode
            print("No mode specified, starting interactive mode...")
            asyncio.run(interactive_mode(agent))
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
