#!/usr/bin/env python3
"""
Demo script for Augment Agent Replica
Demonstrates all implemented features and capabilities
"""

import asyncio
import os
import sys
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from core.agent import AugmentAgentReplica
from utils.config import Config

async def demo_file_management(agent: AugmentAgentReplica, workspace: str):
    """Demonstrate file management capabilities"""
    print("\n🗂️  FILE MANAGEMENT DEMO")
    print("=" * 50)
    
    # Create a sample Python file
    print("\n1. Creating a sample Python file...")
    result = await agent.execute_tool_directly('save-file', {
        'instructions_reminder': 'LIMIT THE FILE CONTENT TO AT MOST 300 LINES. IF MORE CONTENT NEEDS TO BE ADDED USE THE str-replace-editor TOOL TO EDIT THE FILE AFTER IT HAS BEEN CREATED.',
        'path': 'demo_app.py',
        'file_content': '''#!/usr/bin/env python3
"""
Demo application for testing
"""

class Calculator:
    """Simple calculator class"""
    
    def __init__(self):
        self.history = []
    
    def add(self, a, b):
        """Add two numbers"""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result
    
    def multiply(self, a, b):
        """Multiply two numbers"""
        result = a * b
        self.history.append(f"{a} * {b} = {result}")
        return result
    
    def get_history(self):
        """Get calculation history"""
        return self.history

if __name__ == "__main__":
    calc = Calculator()
    print(calc.add(5, 3))
    print(calc.multiply(4, 7))
    print("History:", calc.get_history())
'''
    })
    
    if result['success']:
        print("✅ File created successfully!")
    else:
        print(f"❌ Error: {result['error']}")
        return
    
    # View the file
    print("\n2. Viewing the created file...")
    result = await agent.execute_tool_directly('view', {
        'path': 'demo_app.py',
        'type': 'file'
    })
    
    if result['success']:
        print("✅ File content:")
        print(result['data']['content'][:500] + "..." if len(result['data']['content']) > 500 else result['data']['content'])
    
    # Search for specific patterns
    print("\n3. Searching for class definitions...")
    result = await agent.execute_tool_directly('view', {
        'path': 'demo_app.py',
        'type': 'file',
        'search_query_regex': 'class.*:'
    })
    
    if result['success']:
        print(f"✅ Found {result['data']['matches_found']} class definitions")
        print("Search results:")
        print(result['data']['content'])
    
    # Edit the file
    print("\n4. Adding a new method to the Calculator class...")
    result = await agent.execute_tool_directly('str-replace-editor', {
        'command': 'str_replace',
        'path': 'demo_app.py',
        'instruction_reminder': 'ALWAYS BREAK DOWN EDITS INTO SMALLER CHUNKS OF AT MOST 150 LINES EACH.',
        'old_str_1': '    def get_history(self):\n        """Get calculation history"""\n        return self.history',
        'new_str_1': '''    def get_history(self):
        """Get calculation history"""
        return self.history
    
    def subtract(self, a, b):
        """Subtract two numbers"""
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result''',
        'old_str_start_line_number_1': 24,
        'old_str_end_line_number_1': 26
    })
    
    if result['success']:
        print("✅ File edited successfully!")
        print("Edit snippets:")
        for snippet in result['data']['snippets']:
            print(snippet[:200] + "..." if len(snippet) > 200 else snippet)
    
    # View directory
    print("\n5. Viewing workspace directory...")
    result = await agent.execute_tool_directly('view', {
        'path': '.',
        'type': 'directory'
    })
    
    if result['success']:
        print("✅ Directory listing:")
        print(result['data']['content'])

async def demo_agent_chat(agent: AugmentAgentReplica):
    """Demonstrate chat capabilities"""
    print("\n💬 CHAT DEMO")
    print("=" * 50)
    
    test_messages = [
        "Hello! Can you tell me about your capabilities?",
        "What tools do you have available?",
        "Can you view the demo_app.py file we created?",
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n{i}. User: {message}")
        print("Agent: ", end="")
        
        try:
            async for chunk in agent.chat(message, stream=True):
                if chunk['type'] == 'content':
                    print(chunk['content'], end='', flush=True)
                elif chunk['type'] == 'tool_result':
                    tool_name = chunk['tool_name']
                    success = "✅" if chunk['success'] else "❌"
                    print(f"\n[{success} Tool: {tool_name}]", end='')
                elif chunk['type'] == 'error':
                    print(f"\n❌ Error: {chunk['content']}")
            print()  # New line after response
        except Exception as e:
            print(f"\n❌ Error in chat: {e}")

async def demo_agent_status(agent: AugmentAgentReplica):
    """Demonstrate agent status and statistics"""
    print("\n📊 AGENT STATUS DEMO")
    print("=" * 50)
    
    status = agent.get_agent_status()
    
    print(f"Status: {status['status']}")
    print(f"Tools Registered: {status['tools_registered']}")
    print(f"Conversation Length: {status['conversation_length']}")
    print(f"Model: {status['model_info']['model']} ({status['model_info']['provider']})")
    
    print("\nExecution Statistics:")
    stats = status['execution_stats']
    print(f"  Total Executions: {stats['total_executions']}")
    print(f"  Successful: {stats['successful_executions']}")
    print(f"  Failed: {stats['failed_executions']}")
    print(f"  Success Rate: {stats['success_rate']:.1f}%")
    print(f"  Average Execution Time: {stats['average_execution_time']:.3f}s")
    
    print("\nBehavior Patterns:")
    for pattern, enabled in status['behavior_patterns'].items():
        status_icon = "✅" if enabled else "❌"
        print(f"  {status_icon} {pattern.replace('_', ' ').title()}")

async def demo_available_tools(agent: AugmentAgentReplica):
    """Demonstrate available tools"""
    print("\n🔧 AVAILABLE TOOLS DEMO")
    print("=" * 50)

    tools = agent.get_available_tools()

    print(f"Total Tools Available: {len(tools)}")

    # Group tools by category
    categories = {}
    for tool in tools:
        category = tool['category']
        if category not in categories:
            categories[category] = []
        categories[category].append(tool)

    for category, category_tools in categories.items():
        print(f"\n📁 {category.replace('_', ' ').title()} ({len(category_tools)} tools):")
        for tool in category_tools:
            print(f"  • {tool['name']}: {tool['description'][:80]}...")

async def demo_code_intelligence(agent: AugmentAgentReplica):
    """Demonstrate code intelligence tools"""
    print("\n🧠 CODE INTELLIGENCE DEMO")
    print("=" * 50)

    # Test codebase retrieval
    print("\n1. Testing codebase retrieval...")
    result = await agent.execute_tool_directly('codebase-retrieval', {
        'information_request': 'find Calculator class and its methods'
    })

    if result['success']:
        print(f"✅ Found {result['data']['results_found']} results")
        if result['data']['results']:
            for i, res in enumerate(result['data']['results'][:3], 1):
                print(f"  {i}. {res['name']} ({res['type']}) in {res['file_path']}")
    else:
        print(f"❌ Error: {result['error']}")

    # Test diagnostics
    print("\n2. Testing diagnostics...")
    result = await agent.execute_tool_directly('diagnostics', {
        'paths': ['demo_app.py']
    })

    if result['success']:
        summary = result['data']['summary']
        print(f"✅ Analyzed {summary['files_analyzed']} files")
        print(f"  Issues found: {summary['total_issues']} (Errors: {summary['errors']}, Warnings: {summary['warnings']})")
    else:
        print(f"❌ Error: {result['error']}")

async def demo_process_management(agent: AugmentAgentReplica, workspace: str):
    """Demonstrate process management tools"""
    print("\n⚙️  PROCESS MANAGEMENT DEMO")
    print("=" * 50)

    # Launch a simple process
    print("\n1. Launching a process...")
    result = await agent.execute_tool_directly('launch-process', {
        'command': 'echo "Hello from process!" && sleep 1 && echo "Process completed"',
        'wait': True,
        'max_wait_seconds': 10,
        'cwd': workspace
    })

    if result['success']:
        print(f"✅ Process completed with return code: {result['data']['return_code']}")
        print(f"  Output: {result['data']['stdout']}")
        terminal_id = result['data']['terminal_id']
    else:
        print(f"❌ Error: {result['error']}")
        return

    # List processes
    print("\n2. Listing processes...")
    result = await agent.execute_tool_directly('list-processes', {})

    if result['success']:
        summary = result['data']['summary']
        print(f"✅ Total processes: {summary['total_processes']}")
        print(f"  Running: {summary['running']}, Completed: {summary['completed']}")

    # Read terminal
    print("\n3. Reading terminal output...")
    result = await agent.execute_tool_directly('read-terminal', {})

    if result['success']:
        print("✅ Terminal output retrieved")

async def demo_web_integration(agent: AugmentAgentReplica):
    """Demonstrate web integration tools"""
    print("\n🌐 WEB INTEGRATION DEMO")
    print("=" * 50)

    # Test web search (with mock to avoid rate limits)
    print("\n1. Testing web search...")
    try:
        result = await agent.execute_tool_directly('web-search', {
            'query': 'Python programming tutorial',
            'num_results': 3
        })

        if result['success']:
            print(f"✅ Found {result['data']['num_results_found']} search results")
            for i, res in enumerate(result['data']['results'][:2], 1):
                print(f"  {i}. {res['title'][:60]}...")
        else:
            print(f"❌ Search error: {result['error']}")
    except Exception as e:
        print(f"❌ Search failed: {e}")

    # Test web fetch (with a simple example)
    print("\n2. Testing web content fetch...")
    try:
        result = await agent.execute_tool_directly('web-fetch', {
            'url': 'https://httpbin.org/html'
        })

        if result['success']:
            print(f"✅ Fetched content ({result['data']['content_length']} chars)")
            print(f"  Title: {result['data']['title']}")
        else:
            print(f"❌ Fetch error: {result['error']}")
    except Exception as e:
        print(f"❌ Fetch failed: {e}")

async def demo_task_management(agent: AugmentAgentReplica):
    """Demonstrate task management tools"""
    print("\n📋 TASK MANAGEMENT DEMO")
    print("=" * 50)

    # Add some tasks
    print("\n1. Adding tasks...")
    result = await agent.execute_tool_directly('add_tasks', {
        'tasks': [
            {
                'name': 'Setup Development Environment',
                'description': 'Install Python, IDE, and dependencies',
                'state': 'COMPLETE'
            },
            {
                'name': 'Implement Core Features',
                'description': 'Build the main application functionality',
                'state': 'IN_PROGRESS'
            },
            {
                'name': 'Write Tests',
                'description': 'Create comprehensive test suite',
                'state': 'NOT_STARTED'
            }
        ]
    })

    if result['success']:
        print(f"✅ Created {result['data']['summary']['tasks_created']} tasks")

    # View task list
    print("\n2. Viewing task list...")
    result = await agent.execute_tool_directly('view_tasklist', {})

    if result['success']:
        summary = result['data']['summary']
        print(f"✅ Task list: {summary['total_tasks']} total tasks")
        print(f"  In Progress: {summary['in_progress']}, Complete: {summary['complete']}")
        print("\nTask hierarchy:")
        print(result['data']['markdown'][:200] + "..." if len(result['data']['markdown']) > 200 else result['data']['markdown'])

async def demo_memory_tools(agent: AugmentAgentReplica):
    """Demonstrate memory tools"""
    print("\n💾 MEMORY TOOLS DEMO")
    print("=" * 50)

    # Store a memory
    print("\n1. Storing memory...")
    result = await agent.execute_tool_directly('remember', {
        'memory': 'User prefers Python for backend development and React for frontend'
    })

    if result['success']:
        print(f"✅ Memory stored with ID: {result['data']['memory_id']}")
        print(f"  Importance: {result['data']['importance']:.2f}")
        print(f"  Tags: {result['data']['tags']}")

    # Create a Mermaid diagram
    print("\n2. Creating Mermaid diagram...")
    mermaid_code = '''graph TD
    A[User Request] --> B[Agent Analysis]
    B --> C[Tool Selection]
    C --> D[Tool Execution]
    D --> E[Response Generation]
    E --> F[User Response]'''

    result = await agent.execute_tool_directly('render-mermaid', {
        'diagram_definition': mermaid_code,
        'title': 'Agent Workflow'
    })

    if result['success']:
        print(f"✅ Diagram created: {result['data']['diagram_type']} with {result['data']['node_count']} nodes")
        print(f"  HTML file: {result['data']['html_file']}")
    else:
        print(f"❌ Error: {result['error']}")

async def main():
    """Main demo function"""
    print("🤖 AUGMENT AGENT REPLICA - COMPREHENSIVE DEMO")
    print("=" * 60)
    
    # Create temporary workspace
    with tempfile.TemporaryDirectory() as temp_workspace:
        print(f"📁 Using temporary workspace: {temp_workspace}")
        
        # Setup configuration
        config = Config()
        config.workspace_root = temp_workspace
        
        # Check if API key is available
        if not config.mistral_api_key:
            print("\n⚠️  WARNING: MISTRAL_API_KEY not found in environment")
            print("Some features may not work without a valid API key")
            print("Set MISTRAL_API_KEY environment variable to enable full functionality")
            
            # For demo purposes, we'll use a mock key
            config.mistral_api_key = "demo_key"
        
        try:
            # Initialize agent
            print("\n🚀 Initializing Augment Agent Replica...")
            agent = AugmentAgentReplica(config)
            print("✅ Agent initialized successfully!")
            
            # Run demos
            await demo_available_tools(agent)
            await demo_file_management(agent, temp_workspace)
            await demo_code_intelligence(agent)
            await demo_process_management(agent, temp_workspace)
            await demo_web_integration(agent)
            await demo_task_management(agent)
            await demo_memory_tools(agent)
            await demo_agent_status(agent)

            # Only run chat demo if we have a real API key
            if os.getenv('MISTRAL_API_KEY'):
                await demo_agent_chat(agent)
            else:
                print("\n💬 CHAT DEMO SKIPPED")
                print("Set MISTRAL_API_KEY environment variable to test chat functionality")
            
            print("\n🎉 DEMO COMPLETED SUCCESSFULLY!")
            print("\nNext steps:")
            print("1. Set your MISTRAL_API_KEY environment variable")
            print("2. Run: python src/main.py --interactive")
            print("3. Or start web server: python src/main.py --server")
            
        except Exception as e:
            print(f"\n❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
